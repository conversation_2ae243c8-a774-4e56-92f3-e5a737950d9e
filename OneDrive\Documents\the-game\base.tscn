[gd_scene load_steps=4 format=3 uid="uid://dh87aq4i0qxru"]

[ext_resource type="Script" path="res://character_body_3d.gd" id="1_yjk57"]

[sub_resource type="CapsuleMesh" id="CapsuleMesh_vlg67"]

[sub_resource type="ConvexPolygonShape3D" id="ConvexPolygonShape3D_vgqdw"]
points = PackedVector3Array(-0.5, -0.5, 0, -0.497651, -0.5, -0.0490407, -0.492463, -0.586923, 0, -0.497651, -0.5, 0.0489429, -0.5, 0.5, 0, -0.490407, -0.5, -0.097592, -0.490114, -0.586923, -0.0483555, -0.497651, 0.5, -0.0490407, -0.490114, -0.586923, 0.0482576, -0.469851, -0.671104, 0, -0.490407, -0.5, 0.0974941, -0.497651, 0.5, 0.0489429, -0.492463, 0.586727, 0, -0.478563, -0.5, -0.145164, -0.482968, -0.586923, -0.0961237, -0.490407, 0.5, -0.097592, -0.460846, -0.671104, -0.0917189, -0.4676, -0.671104, -0.0461042, -0.490114, 0.586727, -0.0483555, -0.482968, -0.586923, 0.0960258, -0.4676, -0.671104, 0.0460063, -0.460846, -0.671104, 0.091621, -0.430991, -0.75, -0.0424824, -0.433046, -0.75, 0, -0.430991, -0.75, 0.0423845, -0.478563, -0.5, 0.145067, -0.490407, 0.5, 0.0974941, -0.490114, 0.586727, 0.0482576, -0.469851, 0.670908, 0, -0.46202, -0.5, -0.191366, -0.454973, -0.586923, -0.188528, -0.471222, -0.586923, -0.143011, -0.478563, 0.5, -0.145164, -0.449687, -0.671104, -0.136453, -0.482968, 0.586727, -0.0961237, -0.414448, -0.75, -0.125783, -0.424726, -0.75, -0.0845732, -0.4676, 0.670908, -0.0461042, -0.460846, 0.670908, -0.0917189, -0.471222, -0.586923, 0.142913, -0.449687, -0.671104, 0.136355, -0.424726, -0.75, 0.0844753, -0.414448, -0.75, 0.125685, -0.381265, -0.821457, -0.0375881, -0.383027, -0.821457, 0, -0.381265, -0.821457, 0.0374902, -0.454973, -0.586923, 0.18843, -0.46202, -0.5, 0.191269, -0.478563, 0.5, 0.145067, -0.482968, 0.586727, 0.0960258, -0.460846, 0.670908, 0.091621, -0.4676, 0.670908, 0.0460063, -0.430991, 0.75, 0.0423845, -0.433046, 0.75, 0, -0.430991, 0.75, -0.0424824, -0.440975, -0.5, -0.235709, -0.434319, -0.586923, -0.232185, -0.46202, 0.5, -0.191366, -0.414448, -0.671104, -0.221515, -0.434123, -0.671104, -0.179816, -0.471222, 0.586727, -0.143011, -0.454973, 0.586727, -0.188528, -0.449687, 0.670908, -0.136453, -0.400059, -0.75, -0.16572, -0.353955, -0.821457, -0.146633, -0.366582, -0.821457, -0.111198, -0.375685, -0.821457, -0.0747846, -0.424726, 0.75, -0.0845732, -0.414448, 0.75, -0.125783, -0.434123, -0.671104, 0.179718, -0.375685, -0.821457, 0.0746868, -0.400059, -0.75, 0.165623, -0.366582, -0.821457, 0.1111, -0.353955, -0.821457, 0.146535, -0.31529, -0.883125, -0.0627447, -0.31989, -0.883125, -0.0315192, -0.321457, -0.883125, 0, -0.31989, -0.883125, 0.0314213, -0.31529, -0.883125, 0.0626468, -0.434319, -0.586923, 0.232087, -0.414448, -0.671104, 0.221417, -0.440975, -0.5, 0.235611, -0.46202, 0.5, 0.191269, -0.454973, 0.586727, 0.18843, -0.471222, 0.586727, 0.142913, -0.449687, 0.670908, 0.136355, -0.414448, 0.75, 0.125685, -0.424726, 0.75, 0.0844753, -0.381265, 0.821261, 0.0374902, -0.383027, 0.821261, 0, -0.381265, 0.821261, -0.0375881, -0.415818, -0.5, -0.2778, -0.440975, 0.5, -0.235709, -0.409456, -0.586923, -0.27359, -0.39076, -0.671104, -0.261061, -0.434319, 0.586727, -0.232185, -0.38195, -0.75, -0.20419, -0.434123, 0.670908, -0.179816, -0.414448, 0.670908, -0.221515, -0.337803, -0.821457, -0.180599, -0.283477, -0.883125, -0.151527, -0.296985, -0.883125, -0.123042, -0.307557, -0.883125, -0.0933829, -0.375685, 0.821261, -0.0747846, -0.366582, 0.821261, -0.111198, -0.353955, 0.821261, -0.146633, -0.400059, 0.75, -0.16572, -0.38195, -0.75, 0.204092, -0.307557, -0.883125, 0.093285, -0.337803, -0.821457, 0.180501, -0.296985, -0.883125, 0.122944, -0.283477, -0.883125, 0.151429, -0.23933, -0.933046, -0.0726312, -0.245204, -0.933046, -0.048845, -0.248825, -0.933046, -0.0245693, -0.25, -0.933046, 0, -0.248825, -0.933046, 0.0244714, -0.245204, -0.933046, 0.0487471, -0.23933, -0.933046, 0.0725333, -0.409456, -0.586923, 0.273493, -0.415818, -0.5, 0.277702, -0.39076, -0.671104, 0.260963, -0.440975, 0.5, 0.235611, -0.434319, 0.586727, 0.232087, -0.414448, 0.670908, 0.221417, -0.434123, 0.670908, 0.179718, -0.400059, 0.75, 0.165623, -0.353955, 0.821261, 0.146535, -0.366582, 0.821261, 0.1111, -0.375685, 0.821261, 0.0746868, -0.31529, 0.882929, 0.0626468, -0.31989, 0.882929, 0.0314213, -0.321457, 0.882929, 0, -0.31989, 0.882929, -0.0315192, -0.31529, 0.882929, -0.0627447, -0.386551, -0.5, -0.317247, -0.380677, -0.586923, -0.312451, -0.415818, 0.5, -0.2778, -0.363254, -0.671104, -0.29816, -0.360121, -0.75, -0.240603, -0.39076, 0.670908, -0.261061, -0.409456, 0.586727, -0.27359, -0.31852, -0.821457, -0.212803, -0.38195, 0.75, -0.20419, -0.267326, -0.883125, -0.178641, -0.220536, -0.933046, -0.117854, -0.23101, -0.933046, -0.0957322, -0.307557, 0.882929, -0.0933829, -0.296985, 0.882929, -0.123042, -0.283477, 0.882929, -0.151527, -0.337803, 0.821261, -0.180599, -0.360121, -0.75, 0.240505, -0.31852, -0.821457, 0.212706, -0.267326, -0.883125, 0.178543, -0.23101, -0.933046, 0.0956343, -0.220536, -0.933046, 0.117756, -0.158085, -0.969851, -0.0654855, -0.163665, -0.969851, -0.0497259, -0.167776, -0.969851, -0.033379, -0.170223, -0.969851, -0.0168363, -0.171104, -0.969851, 0, -0.170223, -0.969851, 0.0167385, -0.167776, -0.969851, 0.0332811, -0.163665, -0.969851, 0.049628, -0.158085, -0.969851, 0.0653876, -0.380677, -0.586923, 0.312353, -0.386551, -0.5, 0.31715, -0.415818, 0.5, 0.277702, -0.363254, -0.671104, 0.298062, -0.409456, 0.586727, 0.273493, -0.39076, 0.670908, 0.260963, -0.38195, 0.75, 0.204092, -0.337803, 0.821261, 0.180501, -0.283477, 0.882929, 0.151429, -0.296985, 0.882929, 0.122944, -0.307557, 0.882929, 0.093285, -0.23933, 0.93285, 0.0725333, -0.245204, 0.93285, 0.0487471, -0.248825, 0.93285, 0.0244714, -0.25, 0.93285, 0, -0.248825, 0.93285, -0.0245693, -0.245204, 0.93285, -0.048845, -0.23933, 0.93285, -0.0726312, -0.353563, -0.5, -0.353563, -0.348277, -0.586923, -0.348277, -0.386551, 0.5, -0.317247, -0.380677, 0.586727, -0.312451, -0.332322, -0.671104, -0.332322, -0.334769, -0.75, -0.274765, -0.296104, -0.821457, -0.24305, -0.360121, 0.75, -0.240603, -0.363254, 0.670908, -0.29816, -0.31852, 0.821261, -0.212803, -0.248532, -0.883125, -0.203896, -0.193324, -0.933046, -0.158673, -0.207909, -0.933046, -0.1389, -0.142228, -0.969851, -0.095047, -0.150842, -0.969851, -0.0806578, -0.23101, 0.93285, -0.0957322, -0.220536, 0.93285, -0.117854, -0.267326, 0.882929, -0.178641, -0.334769, -0.75, 0.274667, -0.296104, -0.821457, 0.242952, -0.248532, -0.883125, 0.203798, -0.207909, -0.933046, 0.138802, -0.193324, -0.933046, 0.158575, -0.150842, -0.969851, 0.0805599, -0.142228, -0.969851, 0.0949491, -0.0766445, -0.992561, -0.0410141, -0.0802662, -0.992561, -0.0332811, -0.0831049, -0.992561, -0.0252545, -0.0851605, -0.992561, -0.0170321, -0.0864331, -0.992561, -0.00851604, -0.0868246, -0.992561, 0, -0.0864331, -0.992561, 0.00841814, -0.0851605, -0.992561, 0.0169342, -0.0831049, -0.992561, 0.0251566, -0.0802662, -0.992561, 0.0331832, -0.0766445, -0.992561, 0.0409162, -0.348277, -0.586923, 0.348179, -0.353563, -0.5, 0.353465, -0.386551, 0.5, 0.31715, -0.380677, 0.586727, 0.312353, -0.332322, -0.671104, 0.332224, -0.363254, 0.670908, 0.298062, -0.360121, 0.75, 0.240505, -0.31852, 0.821261, 0.212706, -0.267326, 0.882929, 0.178543, -0.220536, 0.93285, 0.117756, -0.23101, 0.93285, 0.0956343, -0.158085, 0.969655, 0.0653876, -0.163665, 0.969655, 0.049628, -0.167776, 0.969655, 0.0332811, -0.170223, 0.969655, 0.0167385, -0.171104, 0.969655, 0, -0.170223, 0.969655, -0.0168363, -0.167776, 0.969655, -0.033379, -0.163665, 0.969655, -0.0497259, -0.158085, 0.969655, -0.0654855, -0.317247, -0.5, -0.386551, -0.353563, 0.5, -0.353563, -0.312451, -0.586923, -0.380677, -0.29816, -0.671104, -0.363254, -0.348277, 0.586727, -0.348277, -0.274765, -0.75, -0.334769, -0.306186, -0.75, -0.306186, -0.27085, -0.821457, -0.27085, -0.227291, -0.883125, -0.227291, -0.296104, 0.821261, -0.24305, -0.334769, 0.75, -0.274765, -0.332322, 0.670908, -0.332322, -0.176782, -0.933046, -0.176782, -0.120987, -0.969851, -0.120987, -0.132244, -0.969851, -0.108555, -0.0722396, -0.992561, -0.0482576, -0.150842, 0.969655, -0.0806578, -0.142228, 0.969655, -0.095047, -0.207909, 0.93285, -0.1389, -0.193324, 0.93285, -0.158673, -0.248532, 0.882929, -0.203896, -0.306186, -0.75, 0.306089, -0.27085, -0.821457, 0.270752, -0.227291, -0.883125, 0.227193, -0.176782, -0.933046, 0.176684, -0.132244, -0.969851, 0.108457, -0.120987, -0.969851, 0.120889, -0.0722396, -0.992561, 0.0481598, 0, -1, 0, -0.312451, -0.586923, 0.380579, -0.317247, -0.5, 0.386453, -0.29816, -0.671104, 0.363156, -0.353563, 0.5, 0.353465, -0.348277, 0.586727, 0.348179, -0.274765, -0.75, 0.334671, -0.332322, 0.670908, 0.332224, -0.334769, 0.75, 0.274667, -0.296104, 0.821261, 0.242952, -0.248532, 0.882929, 0.203798, -0.193324, 0.93285, 0.158575, -0.207909, 0.93285, 0.138802, -0.142228, 0.969655, 0.0949491, -0.150842, 0.969655, 0.0805599, -0.0766445, 0.992365, 0.0409162, -0.0802662, 0.992365, 0.0331832, -0.0831049, 0.992365, 0.0251566, -0.0851605, 0.992365, 0.0169342, -0.0864331, 0.992365, 0.00841814, -0.0868246, 0.992365, 0, -0.0864331, 0.992365, -0.00851604, -0.0851605, 0.992365, -0.0170321, -0.0831049, 0.992365, -0.0252545, -0.0802662, 0.992365, -0.0332811, -0.0766445, 0.992365, -0.0410141, -0.2778, -0.5, -0.415818, -0.317247, 0.5, -0.386551, -0.27359, -0.586923, -0.409456, -0.261061, -0.671104, -0.39076, -0.240603, -0.75, -0.360121, -0.29816, 0.670908, -0.363254, -0.312451, 0.586727, -0.380677, -0.24305, -0.821457, -0.296104, -0.203896, -0.883125, -0.248532, -0.158673, -0.933046, -0.193324, -0.227291, 0.882929, -0.227291, -0.27085, 0.821261, -0.27085, -0.306186, 0.75, -0.306186, -0.274765, 0.75, -0.334769, -0.108555, -0.969851, -0.132244, -0.0614722, -0.992561, -0.0614722, -0.0671496, -0.992561, -0.0551096, -0.0722396, 0.992365, -0.0482576, -0.132244, 0.969655, -0.108555, -0.120987, 0.969655, -0.120987, -0.176782, 0.93285, -0.176782, -0.24305, -0.821457, 0.296006, -0.203896, -0.883125, 0.248434, -0.158673, -0.933046, 0.193226, -0.0671496, -0.992561, 0.0550117, -0.0614722, -0.992561, 0.0613743, -0.108555, -0.969851, 0.132146, 0.0765466, -0.992561, 0.0409162, 0.0721418, -0.992561, 0.0481598, 0.0670517, -0.992561, 0.0550117, 0.0613743, -0.992561, 0.0613743, 0.0550117, -0.992561, 0.0670517, 0.0481598, -0.992561, 0.0721418, 0.0409162, -0.992561, 0.0765466, 0.0331832, -0.992561, 0.0801684, 0.0251566, -0.992561, 0.083007, 0.0169342, -0.992561, 0.0850626, 0.00841814, -0.992561, 0.0863352, 0, -0.992561, 0.0867267, -0.00851604, -0.992561, 0.0863352, -0.0170321, -0.992561, 0.0850626, -0.0252545, -0.992561, 0.083007, -0.0332811, -0.992561, 0.0801684, -0.0410141, -0.992561, 0.0765466, -0.0482576, -0.992561, 0.0721418, -0.0551096, -0.992561, 0.0670517, -0.0551096, -0.992561, -0.0671496, -0.0482576, -0.992561, -0.0722396, -0.0410141, -0.992561, -0.0766445, -0.0332811, -0.992561, -0.0802662, -0.0252545, -0.992561, -0.0831049, -0.0170321, -0.992561, -0.0851605, -0.00851604, -0.992561, -0.0864331, 0, -0.992561, -0.0868246, 0.00841814, -0.992561, -0.0864331, 0.0169342, -0.992561, -0.0851605, 0.0251566, -0.992561, -0.0831049, 0.0331832, -0.992561, -0.0802662, 0.0409162, -0.992561, -0.0766445, 0.0481598, -0.992561, -0.0722396, 0.0550117, -0.992561, -0.0671496, 0.0613743, -0.992561, -0.0614722, 0.0670517, -0.992561, -0.0551096, 0.0721418, -0.992561, -0.0482576, 0.0765466, -0.992561, -0.0410141, 0.0801684, -0.992561, -0.0332811, 0.083007, -0.992561, -0.0252545, 0.0850626, -0.992561, -0.0170321, 0.0863352, -0.992561, -0.00851604, 0.0867267, -0.992561, 0, 0.0863352, -0.992561, 0.00841814, 0.0850626, -0.992561, 0.0169342, 0.083007, -0.992561, 0.0251566, 0.0801684, -0.992561, 0.0331832, -0.27359, -0.586923, 0.409358, -0.2778, -0.5, 0.41572, -0.261061, -0.671104, 0.390662, -0.317247, 0.5, 0.386453, -0.240603, -0.75, 0.360023, -0.312451, 0.586727, 0.380579, -0.29816, 0.670908, 0.363156, -0.274765, 0.75, 0.334671, -0.306186, 0.75, 0.306089, -0.27085, 0.821261, 0.270752, -0.227291, 0.882929, 0.227193, -0.176782, 0.93285, 0.176684, -0.120987, 0.969655, 0.120889, -0.132244, 0.969655, 0.108457, -0.0722396, 0.992365, 0.0481598, 0, 1, 0, -0.235709, -0.5, -0.440975, -0.232185, -0.586923, -0.434319, -0.2778, 0.5, -0.415818, -0.221515, -0.671104, -0.414448, -0.20419, -0.75, -0.38195, -0.212803, -0.821457, -0.31852, -0.240603, 0.75, -0.360121, -0.261061, 0.670908, -0.39076, -0.27359, 0.586727, -0.409456, -0.178641, -0.883125, -0.267326, -0.1389, -0.933046, -0.207909, -0.095047, -0.969851, -0.142228, -0.158673, 0.93285, -0.193324, -0.203896, 0.882929, -0.248532, -0.24305, 0.821261, -0.296104, -0.0671496, 0.992365, -0.0551096, -0.0614722, 0.992365, -0.0614722, -0.108555, 0.969655, -0.132244, -0.212803, -0.821457, 0.318422, -0.178641, -0.883125, 0.267228, -0.1389, -0.933046, 0.207811, -0.095047, -0.969851, 0.14213, 0.157987, -0.969851, 0.0653876, 0.150744, -0.969851, 0.0805599, 0.14213, -0.969851, 0.0949491, 0.132146, -0.969851, 0.108457, 0.120889, -0.969851, 0.120889, 0.108457, -0.969851, 0.132146, 0.0949491, -0.969851, 0.14213, 0.0805599, -0.969851, 0.150744, 0.0653876, -0.969851, 0.157987, 0.049628, -0.969851, 0.163567, 0.0332811, -0.969851, 0.167678, 0.0167385, -0.969851, 0.170125, 0, -0.969851, 0.171006, -0.0168363, -0.969851, 0.170125, -0.033379, -0.969851, 0.167678, -0.0497259, -0.969851, 0.163567, -0.0654855, -0.969851, 0.157987, -0.0806578, -0.969851, 0.150744, -0.0806578, -0.969851, -0.150842, -0.0654855, -0.969851, -0.158085, -0.0497259, -0.969851, -0.163665, -0.033379, -0.969851, -0.167776, -0.0168363, -0.969851, -0.170223, 0, -0.969851, -0.171104, 0.0167385, -0.969851, -0.170223, 0.0332811, -0.969851, -0.167776, 0.049628, -0.969851, -0.163665, 0.0653876, -0.969851, -0.158085, 0.0805599, -0.969851, -0.150842, 0.0949491, -0.969851, -0.142228, 0.108457, -0.969851, -0.132244, 0.120889, -0.969851, -0.120987, 0.132146, -0.969851, -0.108555, 0.14213, -0.969851, -0.095047, 0.150744, -0.969851, -0.0806578, 0.157987, -0.969851, -0.0654855, 0.163567, -0.969851, -0.0497259, 0.167678, -0.969851, -0.033379, 0.170125, -0.969851, -0.0168363, 0.171006, -0.969851, 0, 0.170125, -0.969851, 0.0167385, 0.167678, -0.969851, 0.0332811, 0.163567, -0.969851, 0.049628, -0.232185, -0.586923, 0.434221, -0.235709, -0.5, 0.440877, -0.2778, 0.5, 0.41572, -0.221515, -0.671104, 0.41435, -0.20419, -0.75, 0.381852, -0.27359, 0.586727, 0.409358, -0.261061, 0.670908, 0.390662, -0.240603, 0.75, 0.360023, -0.24305, 0.821261, 0.296006, -0.203896, 0.882929, 0.248434, -0.158673, 0.93285, 0.193226, -0.108555, 0.969655, 0.132146, -0.0614722, 0.992365, 0.0613743, -0.0671496, 0.992365, 0.0550117, 0.0863352, 0.992365, 0.00841814, 0.0867267, 0.992365, 0, 0.0863352, 0.992365, -0.00851604, 0.0850626, 0.992365, -0.0170321, 0.083007, 0.992365, -0.0252545, 0.0801684, 0.992365, -0.0332811, 0.0765466, 0.992365, -0.0410141, 0.0721418, 0.992365, -0.0482576, 0.0670517, 0.992365, -0.0551096, 0.0613743, 0.992365, -0.0614722, 0.0550117, 0.992365, -0.0671496, 0.0481598, 0.992365, -0.0722396, 0.0409162, 0.992365, -0.0766445, 0.0331832, 0.992365, -0.0802662, 0.0251566, 0.992365, -0.0831049, 0.0169342, 0.992365, -0.0851605, 0.00841814, 0.992365, -0.0864331, 0, 0.992365, -0.0868246, -0.00851604, 0.992365, -0.0864331, -0.0170321, 0.992365, -0.0851605, -0.0252545, 0.992365, -0.0831049, -0.0332811, 0.992365, -0.0802662, -0.0410141, 0.992365, -0.0766445, -0.0482576, 0.992365, -0.0722396, -0.0551096, 0.992365, -0.0671496, -0.0551096, 0.992365, 0.0670517, -0.0482576, 0.992365, 0.0721418, -0.0410141, 0.992365, 0.0765466, -0.0332811, 0.992365, 0.0801684, -0.0252545, 0.992365, 0.083007, -0.0170321, 0.992365, 0.0850626, -0.00851604, 0.992365, 0.0863352, 0, 0.992365, 0.0867267, 0.00841814, 0.992365, 0.0863352, 0.0169342, 0.992365, 0.0850626, 0.0251566, 0.992365, 0.083007, 0.0331832, 0.992365, 0.0801684, 0.0409162, 0.992365, 0.0765466, 0.0481598, 0.992365, 0.0721418, 0.0550117, 0.992365, 0.0670517, 0.0613743, 0.992365, 0.0613743, 0.0670517, 0.992365, 0.0550117, 0.0721418, 0.992365, 0.0481598, 0.0765466, 0.992365, 0.0409162, 0.0801684, 0.992365, 0.0331832, 0.083007, 0.992365, 0.0251566, 0.0850626, 0.992365, 0.0169342, -0.191366, -0.5, -0.46202, -0.235709, 0.5, -0.440975, -0.188528, -0.586923, -0.454973, -0.232185, 0.586727, -0.434319, -0.179816, -0.671104, -0.434123, -0.16572, -0.75, -0.400059, -0.146633, -0.821457, -0.353955, -0.180599, -0.821457, -0.337803, -0.212803, 0.821261, -0.31852, -0.20419, 0.75, -0.38195, -0.221515, 0.670908, -0.414448, -0.151527, -0.883125, -0.283477, -0.117854, -0.933046, -0.220536, -0.095047, 0.969655, -0.142228, -0.1389, 0.93285, -0.207909, -0.178641, 0.882929, -0.267326, -0.180599, -0.821457, 0.337706, -0.151527, -0.883125, 0.283379, -0.117854, -0.933046, 0.220439, 0.239233, -0.933046, 0.0725333, 0.230912, -0.933046, 0.0956343, 0.220439, -0.933046, 0.117756, 0.207811, -0.933046, 0.138802, 0.193226, -0.933046, 0.158575, 0.176684, -0.933046, 0.176684, 0.158575, -0.933046, 0.193226, 0.138802, -0.933046, 0.207811, 0.117756, -0.933046, 0.220439, 0.0956343, -0.933046, 0.230912, 0.0725333, -0.933046, 0.239233, 0.0487471, -0.933046, 0.245106, 0.0244714, -0.933046, 0.248728, 0, -0.933046, 0.249902, -0.0245693, -0.933046, 0.248728, -0.048845, -0.933046, 0.245106, -0.0726312, -0.933046, 0.239233, -0.0957322, -0.933046, 0.230912, -0.0957322, -0.933046, -0.23101, -0.0726312, -0.933046, -0.23933, -0.048845, -0.933046, -0.245204, -0.0245693, -0.933046, -0.248825, 0, -0.933046, -0.25, 0.0244714, -0.933046, -0.248825, 0.0487471, -0.933046, -0.245204, 0.0725333, -0.933046, -0.23933, 0.0956343, -0.933046, -0.23101, 0.117756, -0.933046, -0.220536, 0.138802, -0.933046, -0.207909, 0.158575, -0.933046, -0.193324, 0.176684, -0.933046, -0.176782, 0.193226, -0.933046, -0.158673, 0.207811, -0.933046, -0.1389, 0.220439, -0.933046, -0.117854, 0.230912, -0.933046, -0.0957322, 0.239233, -0.933046, -0.0726312, 0.245106, -0.933046, -0.048845, 0.248728, -0.933046, -0.0245693, 0.249902, -0.933046, 0, 0.248728, -0.933046, 0.0244714, 0.245106, -0.933046, 0.0487471, -0.188528, -0.586923, 0.454875, -0.191366, -0.5, 0.461922, -0.235709, 0.5, 0.440877, -0.232185, 0.586727, 0.434221, -0.179816, -0.671104, 0.434025, -0.16572, -0.75, 0.399961, -0.146633, -0.821457, 0.353857, -0.221515, 0.670908, 0.41435, -0.20419, 0.75, 0.381852, -0.212803, 0.821261, 0.318422, -0.178641, 0.882929, 0.267228, -0.1389, 0.93285, 0.207811, -0.095047, 0.969655, 0.14213, 0.167678, 0.969655, 0.0332811, 0.170125, 0.969655, 0.0167385, 0.171006, 0.969655, 0, 0.170125, 0.969655, -0.0168363, 0.167678, 0.969655, -0.033379, 0.163567, 0.969655, -0.0497259, 0.157987, 0.969655, -0.0654855, 0.150744, 0.969655, -0.0806578, 0.14213, 0.969655, -0.095047, 0.132146, 0.969655, -0.108555, 0.120889, 0.969655, -0.120987, 0.108457, 0.969655, -0.132244, 0.0949491, 0.969655, -0.142228, 0.0805599, 0.969655, -0.150842, 0.0653876, 0.969655, -0.158085, 0.049628, 0.969655, -0.163665, 0.0332811, 0.969655, -0.167776, 0.0167385, 0.969655, -0.170223, 0, 0.969655, -0.171104, -0.0168363, 0.969655, -0.170223, -0.033379, 0.969655, -0.167776, -0.0497259, 0.969655, -0.163665, -0.0654855, 0.969655, -0.158085, -0.0806578, 0.969655, -0.150842, -0.0806578, 0.969655, 0.150744, -0.0654855, 0.969655, 0.157987, -0.0497259, 0.969655, 0.163567, -0.033379, 0.969655, 0.167678, -0.0168363, 0.969655, 0.170125, 0, 0.969655, 0.171006, 0.0167385, 0.969655, 0.170125, 0.0332811, 0.969655, 0.167678, 0.049628, 0.969655, 0.163567, 0.0653876, 0.969655, 0.157987, 0.0805599, 0.969655, 0.150744, 0.0949491, 0.969655, 0.14213, 0.108457, 0.969655, 0.132146, 0.120889, 0.969655, 0.120889, 0.132146, 0.969655, 0.108457, 0.14213, 0.969655, 0.0949491, 0.150744, 0.969655, 0.0805599, 0.157987, 0.969655, 0.0653876, 0.163567, 0.969655, 0.049628, -0.145164, -0.5, -0.478563, -0.191366, 0.5, -0.46202, -0.143011, -0.586923, -0.471222, -0.136453, -0.671104, -0.449687, -0.188528, 0.586727, -0.454973, -0.125783, -0.75, -0.414448, -0.111198, -0.821457, -0.366582, -0.0933829, -0.883125, -0.307557, -0.123042, -0.883125, -0.296985, -0.180599, 0.821261, -0.337803, -0.146633, 0.821261, -0.353955, -0.16572, 0.75, -0.400059, -0.179816, 0.670908, -0.434123, -0.117854, 0.93285, -0.220536, -0.151527, 0.882929, -0.283477, -0.123042, -0.883125, 0.296887, 0.315192, -0.883125, 0.0626468, 0.307459, -0.883125, 0.093285, 0.296887, -0.883125, 0.122944, 0.283379, -0.883125, 0.151429, 0.267228, -0.883125, 0.178543, 0.248434, -0.883125, 0.203798, 0.227193, -0.883125, 0.227193, 0.203798, -0.883125, 0.248434, 0.178543, -0.883125, 0.267228, 0.151429, -0.883125, 0.283379, 0.122944, -0.883125, 0.296887, 0.093285, -0.883125, 0.307459, 0.0626468, -0.883125, 0.315192, 0.0314213, -0.883125, 0.319793, 0, -0.883125, 0.321359, -0.0315192, -0.883125, 0.319793, -0.0627447, -0.883125, 0.315192, -0.0933829, -0.883125, 0.307459, -0.0627447, -0.883125, -0.31529, -0.0315192, -0.883125, -0.31989, 0, -0.883125, -0.321457, 0.0314213, -0.883125, -0.31989, 0.0626468, -0.883125, -0.31529, 0.093285, -0.883125, -0.307557, 0.122944, -0.883125, -0.296985, 0.151429, -0.883125, -0.283477, 0.178543, -0.883125, -0.267326, 0.203798, -0.883125, -0.248532, 0.227193, -0.883125, -0.227291, 0.248434, -0.883125, -0.203896, 0.267228, -0.883125, -0.178641, 0.283379, -0.883125, -0.151527, 0.296887, -0.883125, -0.123042, 0.307459, -0.883125, -0.0933829, 0.315192, -0.883125, -0.0627447, 0.319793, -0.883125, -0.0315192, 0.321359, -0.883125, 0, 0.319793, -0.883125, 0.0314213, -0.143011, -0.586923, 0.471124, -0.145164, -0.5, 0.478465, -0.136453, -0.671104, 0.449589, -0.191366, 0.5, 0.461922, -0.188528, 0.586727, 0.454875, -0.125783, -0.75, 0.41435, -0.111198, -0.821457, 0.366484, -0.179816, 0.670908, 0.434025, -0.16572, 0.75, 0.399961, -0.146633, 0.821261, 0.353857, -0.180599, 0.821261, 0.337706, -0.151527, 0.882929, 0.283379, -0.117854, 0.93285, 0.220439, 0.239233, 0.93285, 0.0725333, 0.245106, 0.93285, 0.0487471, 0.248728, 0.93285, 0.0244714, 0.25, 0.93285, 0, 0.248728, 0.93285, -0.0245693, 0.245106, 0.93285, -0.048845, 0.239233, 0.93285, -0.0726312, 0.230912, 0.93285, -0.0957322, 0.220439, 0.93285, -0.117854, 0.207811, 0.93285, -0.1389, 0.193226, 0.93285, -0.158673, 0.176684, 0.93285, -0.176782, 0.158575, 0.93285, -0.193324, 0.138802, 0.93285, -0.207909, 0.117756, 0.93285, -0.220536, 0.0956343, 0.93285, -0.23101, 0.0725333, 0.93285, -0.23933, 0.0487471, 0.93285, -0.245204, 0.0244714, 0.93285, -0.248825, 0, 0.93285, -0.25, -0.0245693, 0.93285, -0.248825, -0.048845, 0.93285, -0.245204, -0.0726312, 0.93285, -0.23933, -0.0957322, 0.93285, -0.23101, -0.0957322, 0.93285, 0.230912, -0.0726312, 0.93285, 0.239233, -0.048845, 0.93285, 0.245106, -0.0245693, 0.93285, 0.248728, 0, 0.93285, 0.25, 0.0244714, 0.93285, 0.248728, 0.0487471, 0.93285, 0.245106, 0.0725333, 0.93285, 0.239233, 0.0956343, 0.93285, 0.230912, 0.117756, 0.93285, 0.220439, 0.138802, 0.93285, 0.207811, 0.158575, 0.93285, 0.193226, 0.176684, 0.93285, 0.176684, 0.193226, 0.93285, 0.158575, 0.207811, 0.93285, 0.138802, 0.220439, 0.93285, 0.117756, 0.230912, 0.93285, 0.0956343, -0.097592, -0.5, -0.490407, -0.0961237, -0.586923, -0.482968, -0.145164, 0.5, -0.478563, -0.0917189, -0.671104, -0.460846, -0.136453, 0.670908, -0.449687, -0.143011, 0.586727, -0.471222, -0.0845732, -0.75, -0.424726, -0.0747846, -0.821457, -0.375685, -0.125783, 0.75, -0.414448, -0.123042, 0.882929, -0.296985, -0.0933829, 0.882929, -0.307557, -0.111198, 0.821261, -0.366582, 0.381167, -0.821457, 0.0374902, 0.375587, -0.821457, 0.0746868, 0.366484, -0.821457, 0.1111, 0.353857, -0.821457, 0.146535, 0.337706, -0.821457, 0.180501, 0.318422, -0.821457, 0.212706, 0.296006, -0.821457, 0.242952, 0.270752, -0.821457, 0.270752, 0.242952, -0.821457, 0.296006, 0.212706, -0.821457, 0.318422, 0.180501, -0.821457, 0.337706, 0.146535, -0.821457, 0.353857, 0.1111, -0.821457, 0.366484, 0.0746868, -0.821457, 0.375587, 0.0374902, -0.821457, 0.381167, 0, -0.821457, 0.382929, -0.0375881, -0.821457, 0.381167, -0.0747846, -0.821457, 0.375587, -0.0375881, -0.821457, -0.381265, 0, -0.821457, -0.383027, 0.0374902, -0.821457, -0.381265, 0.0746868, -0.821457, -0.375685, 0.1111, -0.821457, -0.366582, 0.146535, -0.821457, -0.353955, 0.180501, -0.821457, -0.337803, 0.212706, -0.821457, -0.31852, 0.242952, -0.821457, -0.296104, 0.270752, -0.821457, -0.27085, 0.296006, -0.821457, -0.24305, 0.318422, -0.821457, -0.212803, 0.337706, -0.821457, -0.180599, 0.353857, -0.821457, -0.146633, 0.366484, -0.821457, -0.111198, 0.375587, -0.821457, -0.0747846, 0.381167, -0.821457, -0.0375881, 0.382929, -0.821457, 0, -0.0961237, -0.586923, 0.48287, -0.097592, -0.5, 0.490309, -0.145164, 0.5, 0.478465, -0.0917189, -0.671104, 0.460748, -0.143011, 0.586727, 0.471124, -0.136453, 0.670908, 0.449589, -0.0845732, -0.75, 0.424628, -0.125783, 0.75, 0.41435, -0.111198, 0.821261, 0.366484, -0.0933829, 0.882929, 0.307459, -0.123042, 0.882929, 0.296887, 0.296887, 0.882929, 0.122944, 0.307459, 0.882929, 0.093285, 0.315192, 0.882929, 0.0626468, 0.319793, 0.882929, 0.0314213, 0.321359, 0.882929, 0, 0.319793, 0.882929, -0.0315192, 0.315192, 0.882929, -0.0627447, 0.307459, 0.882929, -0.0933829, 0.296887, 0.882929, -0.123042, 0.283379, 0.882929, -0.151527, 0.267228, 0.882929, -0.178641, 0.248434, 0.882929, -0.203896, 0.227193, 0.882929, -0.227291, 0.203798, 0.882929, -0.248532, 0.178543, 0.882929, -0.267326, 0.151429, 0.882929, -0.283477, 0.122944, 0.882929, -0.296985, 0.093285, 0.882929, -0.307557, 0.0626468, 0.882929, -0.31529, 0.0314213, 0.882929, -0.31989, 0, 0.882929, -0.321457, -0.0315192, 0.882929, -0.31989, -0.0627447, 0.882929, -0.31529, -0.0627447, 0.882929, 0.315192, -0.0315192, 0.882929, 0.319793, 0, 0.882929, 0.321359, 0.0314213, 0.882929, 0.319793, 0.0626468, 0.882929, 0.315192, 0.093285, 0.882929, 0.307459, 0.122944, 0.882929, 0.296887, 0.151429, 0.882929, 0.283379, 0.178543, 0.882929, 0.267228, 0.203798, 0.882929, 0.248434, 0.227193, 0.882929, 0.227193, 0.248434, 0.882929, 0.203798, 0.267228, 0.882929, 0.178543, 0.283379, 0.882929, 0.151429, -0.0490407, -0.5, -0.497651, -0.0483555, -0.586923, -0.490114, -0.097592, 0.5, -0.490407, -0.0961237, 0.586727, -0.482968, -0.0461042, -0.671104, -0.4676, -0.0424824, -0.75, -0.430991, -0.0917189, 0.670908, -0.460846, -0.0747846, 0.821261, -0.375685, -0.0845732, 0.75, -0.424726, 0.432948, -0.75, 0, 0.430893, -0.75, 0.0423845, 0.424628, -0.75, 0.0844753, 0.41435, -0.75, 0.125685, 0.399961, -0.75, 0.165623, 0.381852, -0.75, 0.204092, 0.360023, -0.75, 0.240505, 0.334671, -0.75, 0.274667, 0.306089, -0.75, 0.306089, 0.274667, -0.75, 0.334671, 0.240505, -0.75, 0.360023, 0.204092, -0.75, 0.381852, 0.165623, -0.75, 0.399961, 0.125685, -0.75, 0.41435, 0.0844753, -0.75, 0.424628, 0.0423845, -0.75, 0.430893, 0, -0.75, 0.432948, -0.0424824, -0.75, 0.430893, 0, -0.75, -0.433046, 0.0423845, -0.75, -0.430991, 0.0844753, -0.75, -0.424726, 0.125685, -0.75, -0.414448, 0.165623, -0.75, -0.400059, 0.204092, -0.75, -0.38195, 0.240505, -0.75, -0.360121, 0.274667, -0.75, -0.334769, 0.306089, -0.75, -0.306186, 0.334671, -0.75, -0.274765, 0.360023, -0.75, -0.240603, 0.381852, -0.75, -0.20419, 0.399961, -0.75, -0.16572, 0.41435, -0.75, -0.125783, 0.424628, -0.75, -0.0845732, 0.430893, -0.75, -0.0424824, -0.0483555, -0.586923, 0.490016, -0.0490407, -0.5, 0.497553, -0.097592, 0.5, 0.490309, -0.0961237, 0.586727, 0.48287, -0.0461042, -0.671104, 0.467502, -0.0917189, 0.670908, 0.460748, -0.0845732, 0.75, 0.424628, -0.0747846, 0.821261, 0.375587, 0.353857, 0.821261, 0.146535, 0.366484, 0.821261, 0.1111, 0.375587, 0.821261, 0.0746868, 0.381167, 0.821261, 0.0374902, 0.382929, 0.821261, 0, 0.381167, 0.821261, -0.0375881, 0.375587, 0.821261, -0.0747846, 0.366484, 0.821261, -0.111198, 0.353857, 0.821261, -0.146633, 0.337706, 0.821261, -0.180599, 0.318422, 0.821261, -0.212803, 0.296006, 0.821261, -0.24305, 0.270752, 0.821261, -0.27085, 0.242952, 0.821261, -0.296104, 0.212706, 0.821261, -0.31852, 0.180501, 0.821261, -0.337803, 0.146535, 0.821261, -0.353955, 0.1111, 0.821261, -0.366582, 0.0746868, 0.821261, -0.375685, 0.0374902, 0.821261, -0.381265, 0, 0.821261, -0.383027, -0.0375881, 0.821261, -0.381265, -0.0375881, 0.821261, 0.381167, 0, 0.821261, 0.382929, 0.0374902, 0.821261, 0.381167, 0.0746868, 0.821261, 0.375587, 0.1111, 0.821261, 0.366484, 0.146535, 0.821261, 0.353857, 0.180501, 0.821261, 0.337706, 0.212706, 0.821261, 0.318422, 0.242952, 0.821261, 0.296006, 0.270752, 0.821261, 0.270752, 0.296006, 0.821261, 0.242952, 0.318422, 0.821261, 0.212706, 0.337706, 0.821261, 0.180501, 0, -0.5, -0.5, 0, -0.586923, -0.492463, -0.0490407, 0.5, -0.497651, 0, -0.671104, -0.469851, -0.0483555, 0.586727, -0.490114, -0.0424824, 0.75, -0.430991, -0.0461042, 0.670908, -0.4676, 0.469753, -0.671104, 0, 0.467502, -0.671104, 0.0460063, 0.460748, -0.671104, 0.091621, 0.449589, -0.671104, 0.136355, 0.434025, -0.671104, 0.179718, 0.41435, -0.671104, 0.221417, 0.390662, -0.671104, 0.260963, 0.363156, -0.671104, 0.298062, 0.332224, -0.671104, 0.332224, 0.298062, -0.671104, 0.363156, 0.260963, -0.671104, 0.390662, 0.221417, -0.671104, 0.41435, 0.179718, -0.671104, 0.434025, 0.136355, -0.671104, 0.449589, 0.091621, -0.671104, 0.460748, 0.0460063, -0.671104, 0.467502, 0, -0.671104, 0.469753, 0.0460063, -0.671104, -0.4676, 0.091621, -0.671104, -0.460846, 0.136355, -0.671104, -0.449687, 0.179718, -0.671104, -0.434123, 0.221417, -0.671104, -0.414448, 0.260963, -0.671104, -0.39076, 0.298062, -0.671104, -0.363254, 0.332224, -0.671104, -0.332322, 0.363156, -0.671104, -0.29816, 0.390662, -0.671104, -0.261061, 0.41435, -0.671104, -0.221515, 0.434025, -0.671104, -0.179816, 0.449589, -0.671104, -0.136453, 0.460748, -0.671104, -0.0917189, 0.467502, -0.671104, -0.0461042, 0, -0.586923, 0.492365, 0, -0.5, 0.5, -0.0490407, 0.5, 0.497553, -0.0483555, 0.586727, 0.490016, -0.0461042, 0.670908, 0.467502, -0.0424824, 0.75, 0.430893, 0.381852, 0.75, 0.204092, 0.399961, 0.75, 0.165623, 0.41435, 0.75, 0.125685, 0.424628, 0.75, 0.0844753, 0.430893, 0.75, 0.0423845, 0.432948, 0.75, 0, 0.430893, 0.75, -0.0424824, 0.424628, 0.75, -0.0845732, 0.41435, 0.75, -0.125783, 0.399961, 0.75, -0.16572, 0.381852, 0.75, -0.20419, 0.360023, 0.75, -0.240603, 0.334671, 0.75, -0.274765, 0.306089, 0.75, -0.306186, 0.274667, 0.75, -0.334769, 0.240505, 0.75, -0.360121, 0.204092, 0.75, -0.38195, 0.165623, 0.75, -0.400059, 0.125685, 0.75, -0.414448, 0.0844753, 0.75, -0.424726, 0.0423845, 0.75, -0.430991, 0, 0.75, -0.433046, 0, 0.75, 0.432948, 0.0423845, 0.75, 0.430893, 0.0844753, 0.75, 0.424628, 0.125685, 0.75, 0.41435, 0.165623, 0.75, 0.399961, 0.204092, 0.75, 0.381852, 0.240505, 0.75, 0.360023, 0.274667, 0.75, 0.334671, 0.306089, 0.75, 0.306089, 0.334671, 0.75, 0.274667, 0.360023, 0.75, 0.240505, 0.0489429, -0.5, -0.497651, 0, 0.5, -0.5, 0.0482576, -0.586923, -0.490114, 0, 0.586727, -0.492463, 0, 0.670908, -0.469851, 0.490016, -0.586923, -0.0483555, 0.492365, -0.586923, 0, 0.490016, -0.586923, 0.0482576, 0.48287, -0.586923, 0.0960258, 0.471124, -0.586923, 0.142913, 0.454875, -0.586923, 0.18843, 0.434221, -0.586923, 0.232087, 0.409358, -0.586923, 0.273493, 0.380579, -0.586923, 0.312353, 0.348179, -0.586923, 0.348179, 0.312353, -0.586923, 0.380579, 0.273493, -0.586923, 0.409358, 0.232087, -0.586923, 0.434221, 0.18843, -0.586923, 0.454875, 0.142913, -0.586923, 0.471124, 0.0960258, -0.586923, 0.48287, 0.0482576, -0.586923, 0.490016, 0.0960258, -0.586923, -0.482968, 0.142913, -0.586923, -0.471222, 0.18843, -0.586923, -0.454973, 0.232087, -0.586923, -0.434319, 0.273493, -0.586923, -0.409456, 0.312353, -0.586923, -0.380677, 0.348179, -0.586923, -0.348277, 0.380579, -0.586923, -0.312451, 0.409358, -0.586923, -0.27359, 0.434221, -0.586923, -0.232185, 0.454875, -0.586923, -0.188528, 0.471124, -0.586923, -0.143011, 0.48287, -0.586923, -0.0961237, 0.0489429, -0.5, 0.497553, 0, 0.5, 0.5, 0, 0.586727, 0.492365, 0, 0.670908, 0.469753, 0.390662, 0.670908, 0.260963, 0.41435, 0.670908, 0.221417, 0.434025, 0.670908, 0.179718, 0.449589, 0.670908, 0.136355, 0.460748, 0.670908, 0.091621, 0.467502, 0.670908, 0.0460063, 0.469753, 0.670908, 0, 0.467502, 0.670908, -0.0461042, 0.460748, 0.670908, -0.0917189, 0.449589, 0.670908, -0.136453, 0.434025, 0.670908, -0.179816, 0.41435, 0.670908, -0.221515, 0.390662, 0.670908, -0.261061, 0.363156, 0.670908, -0.29816, 0.332224, 0.670908, -0.332322, 0.298062, 0.670908, -0.363254, 0.260963, 0.670908, -0.39076, 0.221417, 0.670908, -0.414448, 0.179718, 0.670908, -0.434123, 0.136355, 0.670908, -0.449687, 0.091621, 0.670908, -0.460846, 0.0460063, 0.670908, -0.4676, 0.0460063, 0.670908, 0.467502, 0.091621, 0.670908, 0.460748, 0.136355, 0.670908, 0.449589, 0.179718, 0.670908, 0.434025, 0.221417, 0.670908, 0.41435, 0.260963, 0.670908, 0.390662, 0.298062, 0.670908, 0.363156, 0.332224, 0.670908, 0.332224, 0.363156, 0.670908, 0.298062, 0.0974941, -0.5, -0.490407, 0.0489429, 0.5, -0.497651, 0.0482576, 0.586727, -0.490114, 0.490309, -0.5, -0.097592, 0.497553, -0.5, -0.0490407, 0.5, -0.5, 0, 0.497553, -0.5, 0.0489429, 0.490309, -0.5, 0.0974941, 0.478465, -0.5, 0.145067, 0.461922, -0.5, 0.191269, 0.440877, -0.5, 0.235611, 0.41572, -0.5, 0.277702, 0.386453, -0.5, 0.31715, 0.353465, -0.5, 0.353465, 0.31715, -0.5, 0.386453, 0.277702, -0.5, 0.41572, 0.235611, -0.5, 0.440877, 0.191269, -0.5, 0.461922, 0.145067, -0.5, 0.478465, 0.0974941, -0.5, 0.490309, 0.145067, -0.5, -0.478563, 0.191269, -0.5, -0.46202, 0.235611, -0.5, -0.440975, 0.277702, -0.5, -0.415818, 0.31715, -0.5, -0.386551, 0.353465, -0.5, -0.353563, 0.386453, -0.5, -0.317247, 0.41572, -0.5, -0.2778, 0.440877, -0.5, -0.235709, 0.461922, -0.5, -0.191366, 0.478465, -0.5, -0.145164, 0.0489429, 0.5, 0.497553, 0.0482576, 0.586727, 0.490016, 0.380579, 0.586727, 0.312353, 0.409358, 0.586727, 0.273493, 0.434221, 0.586727, 0.232087, 0.454875, 0.586727, 0.18843, 0.471124, 0.586727, 0.142913, 0.48287, 0.586727, 0.0960258, 0.490016, 0.586727, 0.0482576, 0.492365, 0.586727, 0, 0.490016, 0.586727, -0.0483555, 0.48287, 0.586727, -0.0961237, 0.471124, 0.586727, -0.143011, 0.454875, 0.586727, -0.188528, 0.434221, 0.586727, -0.232185, 0.409358, 0.586727, -0.27359, 0.380579, 0.586727, -0.312451, 0.348179, 0.586727, -0.348277, 0.312353, 0.586727, -0.380677, 0.273493, 0.586727, -0.409456, 0.232087, 0.586727, -0.434319, 0.18843, 0.586727, -0.454973, 0.142913, 0.586727, -0.471222, 0.0960258, 0.586727, -0.482968, 0.0960258, 0.586727, 0.48287, 0.142913, 0.586727, 0.471124, 0.18843, 0.586727, 0.454875, 0.232087, 0.586727, 0.434221, 0.273493, 0.586727, 0.409358, 0.312353, 0.586727, 0.380579, 0.348179, 0.586727, 0.348179, 0.0974941, 0.5, -0.490407, 0.490309, 0.5, -0.097592, 0.497553, 0.5, -0.0490407, 0.5, 0.5, 0, 0.497553, 0.5, 0.0489429, 0.490309, 0.5, 0.0974941, 0.478465, 0.5, 0.145067, 0.461922, 0.5, 0.191269, 0.440877, 0.5, 0.235611, 0.41572, 0.5, 0.277702, 0.386453, 0.5, 0.31715, 0.353465, 0.5, 0.353465, 0.31715, 0.5, 0.386453, 0.277702, 0.5, 0.41572, 0.235611, 0.5, 0.440877, 0.191269, 0.5, 0.461922, 0.145067, 0.5, 0.478465, 0.0974941, 0.5, 0.490309, 0.145067, 0.5, -0.478563, 0.191269, 0.5, -0.46202, 0.235611, 0.5, -0.440975, 0.277702, 0.5, -0.415818, 0.31715, 0.5, -0.386551, 0.353465, 0.5, -0.353563, 0.386453, 0.5, -0.317247, 0.41572, 0.5, -0.2778, 0.440877, 0.5, -0.235709, 0.461922, 0.5, -0.191366, 0.478465, 0.5, -0.145164)

[node name="World" type="Node3D"]

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(-0.893371, -0.0383794, 0.447677, 0, 0.996345, 0.0854169, -0.449319, 0.076309, -0.890106, 0, 0, 0)
light_color = Color(1, 0.572549, 0.313726, 1)
shadow_enabled = true

[node name="CSGBox3D" type="CSGBox3D" parent="."]
calculate_tangents = false
use_collision = true
size = Vector3(39.3778, 0.2, 66.9095)

[node name="CharacterBody3D" type="CharacterBody3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.44826, 0)
script = ExtResource("1_yjk57")

[node name="MeshInstance3D" type="MeshInstance3D" parent="CharacterBody3D"]
mesh = SubResource("CapsuleMesh_vlg67")

[node name="CollisionShape3D" type="CollisionShape3D" parent="CharacterBody3D"]
shape = SubResource("ConvexPolygonShape3D_vgqdw")

[node name="Head" type="Node3D" parent="CharacterBody3D"]

[node name="Camera3D" type="Camera3D" parent="CharacterBody3D/Head"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0180988, 0.615643, -0.189648)
